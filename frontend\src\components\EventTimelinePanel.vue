<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📊</span>
        <span>事件时间线</span>
      </div>
      <el-button size="small" type="primary" @click="addEvent">
        <el-icon><Plus /></el-icon>
        新增
      </el-button>
    </div>

    <div class="events-timeline" v-loading="eventsLoading" element-loading-text="正在加载事件线...">
      <div v-for="event in events" :key="event.id" class="event-item">
        <div class="event-marker"></div>
        <div class="event-content">
          <div class="event-header">
            <h4>{{ event.title }}</h4>
            <div class="event-actions">
              <el-dropdown @command="(cmd) => handleEventAction(cmd, event)" trigger="click">
                <el-button size="small" type="text" @click.stop>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <el-tooltip
            :content="event.description"
            placement="right"
            :disabled="event.description.length <= 80"
            effect="light"
            :show-after="300"
          >
            <p class="event-desc event-desc-truncated">
              {{ event.description.length > 80 ? event.description.substring(0, 80) + '...' : event.description }}
            </p>
          </el-tooltip>
          <div class="event-meta">
            <el-tag size="small">{{ event.chapter }}</el-tag>
            <span class="event-time">{{ event.time }}</span>
          </div>
        </div>
      </div>

      <div v-if="events.length === 0" class="empty-state">
        <p>暂无事件记录</p>
        <el-button size="small" @click="addEvent">添加第一个事件</el-button>
      </div>
    </div>

    <!-- 事件编辑对话框 -->
    <EventEditDialog
      v-model="showEventDialog"
      :event-form="eventForm"
      :chapters="chapters"
      @save="saveEvent"
      @cancel="showEventDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled, Edit, Delete } from '@element-plus/icons-vue'
import { eventApi } from '@/services/novelApi'
import EventEditDialog from './writer/EventEditDialog.vue'

// Props
const props = defineProps({
  novelId: {
    type: [String, Number],
    required: true
  },
  chapters: {
    type: Array,
    default: () => []
  },
  eventsLoading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'update:events',
  'update:eventsLoading',
  'events-updated'
])

// 响应式数据
const events = ref([])
const showEventDialog = ref(false)
const eventForm = ref({
  id: null,
  title: '',
  description: '',
  chapter: '',
  time: '',
  importance: 'normal'
})

// 计算属性
const eventsLoading = computed({
  get: () => props.eventsLoading,
  set: (value) => emit('update:eventsLoading', value)
})

// 方法
const addEvent = () => {
  eventForm.value = {
    id: null,
    title: '',
    description: '',
    chapter: '',
    time: '',
    importance: 'normal'
  }
  showEventDialog.value = true
}

const editEvent = (event) => {
  eventForm.value = {
    id: event.id,
    title: event.title,
    description: event.description || '',
    chapter: event.chapter || '',
    time: event.time || '',
    importance: event.importance || 'normal'
  }
  showEventDialog.value = true
}

const saveEvent = async (formData) => {
  // 如果传入了表单数据，使用传入的数据；否则使用组件内部的表单数据
  const dataToSave = formData || eventForm.value

  if (!dataToSave.title.trim()) {
    ElMessage.warning('请输入事件标题')
    return
  }

  if (!props.novelId) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    if (dataToSave.id) {
      // 编辑现有事件
      const eventData = {
        title: dataToSave.title,
        description: dataToSave.description,
        chapter: dataToSave.chapter,
        time: dataToSave.time,
        importance: dataToSave.importance
      }

      const updatedEvent = await eventApi.updateEvent(
        props.novelId,
        dataToSave.id,
        eventData
      )

      // 更新本地数据
      const index = events.value.findIndex(e => e.id === dataToSave.id)
      if (index > -1) {
        events.value[index] = {
          ...updatedEvent,
          createdAt: new Date(updatedEvent.createdAt),
          updatedAt: new Date(updatedEvent.updatedAt)
        }
      }
      ElMessage.success('事件信息已更新')
    } else {
      // 新增事件
      const eventData = {
        title: dataToSave.title,
        description: dataToSave.description,
        chapter: dataToSave.chapter,
        time: dataToSave.time,
        importance: dataToSave.importance
      }

      const newEvent = await eventApi.createEvent(props.novelId, eventData)

      // 添加到本地事件列表
      events.value.push({
        ...newEvent,
        createdAt: new Date(newEvent.createdAt),
        updatedAt: new Date(newEvent.updatedAt)
      })

      ElMessage.success('事件创建成功')
    }
  } catch (error) {
    console.error('保存事件失败:', error)
    ElMessage.error('保存事件失败: ' + (error.message || '未知错误'))
    return
  }

  showEventDialog.value = false
  emit('update:events', events.value)
  emit('events-updated', events.value)
}

const deleteEvent = (event) => {
  ElMessageBox.confirm(`确定要删除事件《${event.title}》吗？`, '确认删除', {
    type: 'warning'
  }).then(async () => {
    if (!props.novelId) {
      ElMessage.error('小说信息不存在')
      return
    }

    try {
      // 调用后端API删除事件
      await eventApi.deleteEvent(props.novelId, event.id)

      // 从本地事件列表中移除
      const index = events.value.findIndex(e => e.id === event.id)
      if (index > -1) {
        events.value.splice(index, 1)
        ElMessage.success('事件已删除')
        emit('update:events', events.value)
        emit('events-updated', events.value)
      }
    } catch (error) {
      console.error('删除事件失败:', error)
      ElMessage.error('删除事件失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {})
}

// 处理事件操作
const handleEventAction = (command, event) => {
  switch (command) {
    case 'edit':
      editEvent(event)
      break
    case 'delete':
      deleteEvent(event)
      break
  }
}

// 加载事件数据
const loadEvents = async () => {
  if (!props.novelId) return

  try {
    eventsLoading.value = true
    // 从后端API加载事件数据
    const response = await eventApi.getEventsList(props.novelId)
    console.log('事件数据响应:', response)

    // 处理后端响应数据结构
    let eventsData = []
    if (response && response.data) {
      // 如果是标准的 Result 格式 {code, message, data}
      if (Array.isArray(response.data)) {
        eventsData = response.data
      } else {
        console.warn('response.data 不是数组格式:', response.data)
        eventsData = []
      }
    } else if (response && Array.isArray(response)) {
      // 直接数组格式
      eventsData = response
    } else {
      console.warn('未知的响应数据格式:', response)
      eventsData = []
    }

    events.value = eventsData.map(event => ({
      ...event,
      createdAt: event.createdAt ? new Date(event.createdAt) : new Date(),
      updatedAt: event.updatedAt ? new Date(event.updatedAt) : new Date()
    }))

    console.log('从后端加载事件数据:', events.value.length, '个事件')
    emit('update:events', events.value)
    emit('events-updated', events.value)
  } catch (error) {
    console.error('加载事件数据失败:', error)
    ElMessage.warning('事件数据加载失败')
  } finally {
    eventsLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  loadEvents,
  addEvent,
  events: computed(() => events.value)
})

// 生命周期
onMounted(() => {
  if (props.novelId) {
    loadEvents()
  }
})
</script>

<style scoped>
.events-timeline {
  max-height: calc(100vh - 190px);
  overflow-y: auto;
}

.event-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.event-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.event-marker {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #409eff;
  margin-right: 10px;
  margin-top: 6px;
  flex-shrink: 0;
}

.event-content {
  flex: 1;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.event-content h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.event-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.event-item:hover .event-actions {
  opacity: 1;
}

.event-desc-truncated {
  cursor: help;
  transition: color 0.2s ease;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin: 4px 0;
}

.event-desc-truncated:hover {
  color: #303133;
}

.event-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 8px;
}

.event-time {
  margin-left: 8px;
  color: #c0c4cc;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-state p {
  margin-bottom: 16px;
}
</style>
