<template>
  <el-dialog 
    :model-value="visible" 
    @update:model-value="$emit('update:visible', $event)"
    title="选择提示词" 
    width="800px" 
    @close="resetDialog"
  >
    <div class="prompt-dialog-content">
      <!-- 提示词列表 -->
      <div class="prompt-list">
        <h4>{{ getCategoryName() }} 提示词</h4>
        <div class="prompt-cards">
          <div
            v-for="prompt in getPromptsByCategory()"
            :key="prompt.id"
            class="prompt-card"
            :class="{ active: selectedPrompt?.id === prompt.id }"
            @click="selectPrompt(prompt)"
          >
            <div class="prompt-card-header">
              <h5>{{ prompt.title }}</h5>
            </div>
            <div class="prompt-card-description">
              <p>{{ prompt.description }}</p>
            </div>
            <div class="prompt-card-tags">
              <el-tag v-for="tag in prompt.tags" :key="tag" size="small">{{ tag }}</el-tag>
            </div>
          </div>
        </div>

        <div v-if="getPromptsByCategory().length === 0" class="empty-prompts">
          <p>暂无该类型的提示词</p>
          <el-button type="primary" @click="$emit('go-to-library')">去提示词库添加</el-button>
        </div>
      </div>

      <!-- 变量填充区域 -->
      <div v-if="selectedPrompt && Object.keys(promptVariables).length > 0" class="prompt-variables">
        <h4>填充变量</h4>
        <el-form label-width="120px" size="small">
          <el-form-item
            v-for="(value, variable) in promptVariables"
            :key="variable"
            :label="variable + '：'"
          >
            <el-input
              v-model="promptVariables[variable]"
              :placeholder="'请输入' + variable"
              @input="generateFinalPrompt"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 最终提示词预览 -->
      <div v-if="selectedPrompt" class="final-prompt">
        <h4>最终提示词预览</h4>
        <el-input
          v-model="finalPrompt"
          type="textarea"
          :rows="8"
          readonly
          placeholder="请先选择提示词并填充变量"
        />
      </div>
    </div>

    <!-- 批量章节生成时的流式内容显示 -->
    <div v-if="isStreaming && showStreaming" class="streaming-content-area">
      <el-card shadow="never" class="streaming-card">
        <template #header>
          <div class="streaming-header">
            <span>🔄 AI正在批量生成章节大纲...</span>
            <el-tag type="success" size="small">实时生成中...</el-tag>
            <el-button size="small" @click="$emit('stop-streaming')">停止生成</el-button>
          </div>
        </template>
        <div class="streaming-content">
          <pre class="streaming-text-plain">{{ streamingContent }}</pre>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button v-if="selectedPrompt" @click="$emit('copy-prompt', finalPrompt)">复制提示词</el-button>
      <el-button 
        v-if="selectedPrompt" 
        type="primary" 
        @click="handleUsePrompt" 
        :loading="isStreaming"
      >
        {{ isStreaming ? '生成中...' : '使用此提示词' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    default: ''
  },
  availablePrompts: {
    type: Array,
    default: () => []
  },
  isStreaming: {
    type: Boolean,
    default: false
  },
  streamingContent: {
    type: String,
    default: ''
  },
  showStreaming: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'update:visible',
  'prompt-selected',
  'copy-prompt',
  'use-prompt',
  'go-to-library',
  'stop-streaming'
])

// 响应式数据
const selectedPrompt = ref(null)
const promptVariables = ref({})
const finalPrompt = ref('')

// 计算属性
const getCategoryName = () => {
  const categoryNames = {
    outline: '章节大纲',
    content: '基础正文',
    'content-dialogue': '对话生成',
    'content-scene': '场景描写',
    'content-action': '动作情节',
    'content-psychology': '心理描写',
    polish: '文本优化',
    continue: '智能续写',
    character: '人物生成',
    worldview: '世界观生成'
  }
  return categoryNames[props.category] || '提示词'
}

const getPromptsByCategory = () => {
  if (!props.category) return props.availablePrompts
  return props.availablePrompts.filter(prompt => prompt.category === props.category)
}

// 方法
const selectPrompt = (prompt) => {
  selectedPrompt.value = prompt
  promptVariables.value = {}
  
  // 提取变量
  const matches = prompt.content.match(/\{([^}]+)\}/g)
  if (matches) {
    matches.forEach(match => {
      const variable = match.slice(1, -1)
      promptVariables.value[variable] = ''
    })
  }
  
  generateFinalPrompt()
  emit('prompt-selected', { prompt, variables: promptVariables.value, finalPrompt: finalPrompt.value })
}

const generateFinalPrompt = () => {
  if (!selectedPrompt.value) {
    finalPrompt.value = ''
    return
  }
  
  let result = selectedPrompt.value.content
  Object.keys(promptVariables.value).forEach(variable => {
    const value = promptVariables.value[variable] || `{${variable}}`
    result = result.replace(new RegExp(`\\{${variable}\\}`, 'g'), value)
  })
  
  finalPrompt.value = result
}

const resetDialog = () => {
  selectedPrompt.value = null
  promptVariables.value = {}
  finalPrompt.value = ''
}

const handleUsePrompt = () => {
  if (!selectedPrompt.value || !finalPrompt.value) {
    ElMessage.warning('请选择提示词并填充变量')
    return
  }
  
  emit('use-prompt', {
    prompt: selectedPrompt.value,
    variables: promptVariables.value,
    finalPrompt: finalPrompt.value
  })
}

// 监听变量变化
watch(promptVariables, () => {
  generateFinalPrompt()
}, { deep: true })

// 监听对话框关闭
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetDialog()
  }
})
</script>

<style scoped>
/* 提示词对话框样式 */
.prompt-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.prompt-list h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.prompt-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.prompt-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #ffffff;
}

.prompt-card:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.1);
}

.prompt-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.prompt-card-header h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.prompt-card-description p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.prompt-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.prompt-card-tags .el-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

.empty-prompts {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.prompt-variables {
  margin: 20px 0;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.prompt-variables h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
}

.final-prompt {
  margin-top: 20px;
}

.final-prompt h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

/* 流式内容样式 */
.streaming-content-area {
  margin-top: 20px;
}

.streaming-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.streaming-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
}

.streaming-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.streaming-text-plain {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}
</style>
