<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📝</span>
        <span>章节列表</span>
      </div>
      <el-dropdown @command="handleChapterCommand">
        <el-button size="small" type="primary">
          <el-icon><Plus /></el-icon>
          新增章节 <el-icon><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="manual">手动创建</el-dropdown-item>
            <el-dropdown-item command="ai-single">AI生成单章</el-dropdown-item>
            <el-dropdown-item command="ai-batch">AI批量生成</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="chapters-list" v-loading="chaptersLoading" element-loading-text="正在加载章节...">
      <div
        v-for="(chapter, index) in chapters"
        :key="chapter.id"
        class="chapter-item"
        :class="{ active: currentChapter?.id === chapter.id }"
        @click="selectChapter(chapter)"
      >
        <div class="chapter-info">
          <h4>第{{ index + 1 }}章</h4>
          <p>{{ chapter.title }}</p>
          <div class="chapter-meta">
            <span>{{ chapter.wordCount || 0 }}字</span>
            <el-tag v-if="chapter.status" :type="getChapterStatusType(chapter.status)" size="small">
              {{ getChapterStatusText(chapter.status) }}
            </el-tag>
          </div>
          <el-tooltip
            v-if="chapter.description"
            :content="chapter.description"
            placement="top-start"
            :disabled="chapter.description.length <= 50"
            effect="light"
            :show-after="300"
          >
            <p class="chapter-desc chapter-desc-truncated">
              {{ chapter.description.length > 50 ? chapter.description.substring(0, 50) + '...' : chapter.description }}
            </p>
          </el-tooltip>
        </div>
        <div class="chapter-actions">
          <el-dropdown @command="(cmd) => handleChapterAction(cmd, chapter)">
            <el-button size="small" type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                <el-dropdown-item command="generate">AI生成正文</el-dropdown-item>
                <el-dropdown-item divided command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="chapters.length === 0" class="empty-chapters">
        <p>暂无章节</p>
        <el-button size="small" type="primary" @click="addNewChapter">
          创建第一章
        </el-button>
      </div>
    </div>

    <!-- 章节编辑对话框 -->
    <el-dialog v-model="showChapterDialog" :title="editingChapter ? '编辑章节' : '新增章节'" width="600px">
      <el-form :model="chapterForm" label-width="80px">
        <el-form-item label="章节标题">
          <el-input v-model="chapterForm.title" placeholder="请输入章节标题" />
        </el-form-item>
        <el-form-item label="章节简介">
          <div class="form-item-with-ai">
            <el-input
              v-model="chapterForm.description"
              type="textarea"
              :rows="4"
              placeholder="简要描述本章节内容..."
            />
            <el-button
              size="small"
              type="primary"
              @click="generateChapterOutline"
              :loading="isGeneratingOutline"
              style="margin-top: 8px;"
            >
              <el-icon><Star /></el-icon>
              AI生成大纲
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="章节状态">
          <el-select v-model="chapterForm.status">
            <el-option label="草稿" value="draft" />
            <el-option label="完成" value="completed" />
            <el-option label="发表" value="published" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showChapterDialog = false">取消</el-button>
        <el-button type="primary" @click="saveChapter">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown, MoreFilled, Star } from '@element-plus/icons-vue'
import { chapterApi } from '../../services/novelApi.js'
import apiService from '../../services/api.js'

// Props
const props = defineProps({
  chapters: {
    type: Array,
    required: true
  },
  currentChapter: {
    type: Object,
    default: null
  },
  chaptersLoading: {
    type: Boolean,
    default: false
  },
  currentNovel: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'update:chapters',
  'update:currentChapter',
  'select-chapter',
  'chapter-command',
  'chapter-action',
  'open-chapter-generate-dialog'
])

// 响应式数据
const showChapterDialog = ref(false)
const editingChapter = ref(null)
const isGeneratingOutline = ref(false)
const chapterForm = ref({
  title: '',
  description: '',
  status: 'draft'
})

// 章节选择
const selectChapter = (chapter) => {
  emit('select-chapter', chapter)
}

// 章节命令处理
const handleChapterCommand = (command) => {
  if (command === 'manual') {
    addNewChapter()
  } else {
    emit('chapter-command', command)
  }
}

// 章节操作处理
const handleChapterAction = (command, chapter) => {
  switch (command) {
    case 'edit':
      editChapterTitle(chapter)
      break
    case 'generate':
      emit('open-chapter-generate-dialog', chapter)
      break
    case 'delete':
      deleteChapter(chapter)
      break
  }
}

// 新增章节
const addNewChapter = () => {
  editingChapter.value = null
  chapterForm.value = {
    title: '',
    description: '',
    status: 'draft'
  }
  showChapterDialog.value = true
}

// 编辑章节标题
const editChapterTitle = (chapter) => {
  editingChapter.value = chapter
  chapterForm.value = {
    title: chapter.title,
    description: chapter.description || '',
    status: chapter.status || 'draft'
  }
  showChapterDialog.value = true
}

// 保存章节
const saveChapter = async () => {
  if (!chapterForm.value.title.trim()) {
    ElMessage.warning('请输入章节标题')
    return
  }

  if (!props.currentNovel?.id) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    if (editingChapter.value) {
      // 编辑现有章节
      const chapterData = {
        title: chapterForm.value.title,
        outline: chapterForm.value.description,
        status: chapterForm.value.status,
        content: editingChapter.value.content || '',
        wordCount: editingChapter.value.wordCount || 0
      }

      const updatedChapter = await chapterApi.updateChapter(
        props.currentNovel.id,
        editingChapter.value.id,
        chapterData
      )

      // 更新本地数据
      Object.assign(editingChapter.value, updatedChapter)
      ElMessage.success('章节信息已更新')
    } else {
      // 新增章节
      const chapterData = {
        title: chapterForm.value.title,
        outline: chapterForm.value.description,
        status: chapterForm.value.status,
        content: '',
        wordCount: 0
      }

      const newChapter = await chapterApi.createChapter(props.currentNovel.id, chapterData)

      // 添加到本地章节列表
      const updatedChapters = [...props.chapters, {
        ...newChapter,
        createdAt: new Date(newChapter.createdAt),
        updatedAt: new Date(newChapter.updatedAt)
      }]
      
      emit('update:chapters', updatedChapters)
      ElMessage.success('章节创建成功')

      // 自动选择新章节
      setTimeout(() => {
        selectChapter(newChapter)
      }, 100)
    }
  } catch (error) {
    console.error('保存章节失败:', error)
    ElMessage.error('保存章节失败: ' + (error.message || '未知错误'))
    return
  }

  showChapterDialog.value = false
}

// 删除章节
const deleteChapter = (chapter) => {
  ElMessageBox.confirm(`确定要删除章节《${chapter.title}》吗？`, '确认删除', {
    type: 'warning'
  }).then(async () => {
    if (!props.currentNovel?.id) {
      ElMessage.error('小说信息不存在')
      return
    }

    try {
      // 调用后端API删除章节
      await chapterApi.deleteChapter(props.currentNovel.id, chapter.id)

      // 从本地章节列表中移除
      const updatedChapters = props.chapters.filter(c => c.id !== chapter.id)
      emit('update:chapters', updatedChapters)

      // 如果删除的是当前章节
      if (props.currentChapter?.id === chapter.id) {
        emit('update:currentChapter', null)

        // 如果还有其他章节，自动选择第一个章节
        if (updatedChapters.length > 0) {
          setTimeout(() => {
            selectChapter(updatedChapters[0])
          }, 100)
        }
      }

      ElMessage.success('章节已删除')
    } catch (error) {
      console.error('删除章节失败:', error)
      ElMessage.error('删除章节失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {})
}

// 生成章节大纲
const generateChapterOutline = async () => {
  if (!apiService.getConfig().apiKey) {
    ElMessage.warning('请先配置AI API密钥')
    return
  }

  isGeneratingOutline.value = true
  
  try {
    const prompt = `请为小说《${props.currentNovel.title}》生成一个章节大纲。

章节标题：${chapterForm.value.title}

要求：
1. 大纲应该详细描述本章的主要情节
2. 包含关键场景和人物互动
3. 字数控制在200-300字
4. 符合小说的整体风格和设定

请生成详细的章节大纲：`

    const aiResponse = await apiService.generateTextStream(prompt, {
      maxTokens: null,
      temperature: 0.8,
      type: 'outline'
    }, (chunk, fullContent) => {
      chapterForm.value.description = fullContent
    })

    if (!aiResponse.trim()) {
      throw new Error('AI返回内容为空')
    }
    ElMessage.success('章节大纲生成成功')
  } catch (error) {
    console.error('AI生成大纲失败:', error)
    ElMessage.error(`大纲生成失败: ${error.message}`)
  } finally {
    isGeneratingOutline.value = false
  }
}

// 获取章节状态类型
const getChapterStatusType = (status) => {
  const statusMap = {
    draft: 'warning',
    completed: 'success',
    published: 'primary'
  }
  return statusMap[status] || 'warning'
}

// 获取章节状态文本
const getChapterStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    completed: '完成',
    published: '发表'
  }
  return statusMap[status] || '草稿'
}

// 暴露方法给父组件
defineExpose({
  addNewChapter
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.chapters-list {
  max-height: calc(100vh - 190px);
  overflow-y: auto;
}

.chapter-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.chapter-item.active {
  border-color: #409eff;
  background-color: #e6f4ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.chapter-info {
  flex: 1;
}

.chapter-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.chapter-info p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.chapter-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 8px;
  align-items: center;
}

.chapter-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.3;
}

.chapter-desc-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.chapter-desc-truncated:hover {
  color: #606266;
}

.chapter-actions {
  display: flex;
  gap: 4px;
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.form-item-with-ai {
  width: 100%;
}
</style>
