<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📚</span>
        <span>语料库</span>
      </div>
      <el-button size="small" type="primary" @click="addCorpus">
        <el-icon><Plus /></el-icon>
        新增
      </el-button>
    </div>

    <div class="corpus-list" v-loading="corpusLoading" element-loading-text="正在加载语料库...">
      <div v-for="corpus in corpusData" :key="corpus.id" class="corpus-item">
        <div class="corpus-content">
          <div class="corpus-header">
            <h4>{{ corpus.title }}</h4>
            <el-tag :type="getCorpusType(corpus.type)">{{ getCorpusTypeText(corpus.type) }}</el-tag>
          </div>
          <el-tooltip
            :content="corpus.content"
            placement="right"
            :disabled="corpus.content.length <= 100"
            effect="light"
            :show-after="300"
          >
            <p class="corpus-preview corpus-preview-truncated">
              {{ corpus.content.length > 100 ? corpus.content.substring(0, 100) + '...' : corpus.content }}
            </p>
          </el-tooltip>
        </div>
        <div class="corpus-actions">
          <el-button size="small" @click="editCorpus(corpus)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteCorpus(corpus)">删除</el-button>
        </div>
      </div>

      <div v-if="corpusData.length === 0" class="empty-state">
        <p>暂无语料数据</p>
        <el-button size="small" @click="addCorpus">添加第一个语料</el-button>
      </div>
    </div>

    <!-- 语料库编辑对话框 -->
    <el-dialog v-model="showCorpusDialog" title="编辑语料" width="700px">
      <el-form :model="corpusForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="corpusForm.title" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="corpusForm.type">
            <el-option label="场景描述" value="description" />
            <el-option label="对话模板" value="dialogue" />
            <el-option label="情感表达" value="emotion" />
            <el-option label="动作描写" value="action" />
            <el-option label="心理描写" value="psychology" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容">
          <el-input v-model="corpusForm.content" type="textarea" :rows="8" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCorpusDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCorpus">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { corpusApi } from '@/services/novelApi.js'

// Props
const props = defineProps({
  novelId: {
    type: [String, Number],
    required: true
  }
})

// Emits
const emit = defineEmits(['corpus-updated'])

// 响应式数据
const corpusData = ref([])
const corpusLoading = ref(false)
const showCorpusDialog = ref(false)

const corpusForm = ref({
  id: null,
  title: '',
  type: 'description',
  content: '',
  tags: []
})

// 计算属性
const corpusCount = computed(() => corpusData.value.length)

// 方法
const getCorpusType = (type) => {
  const typeMap = {
    'description': 'success',
    'dialogue': 'primary',
    'emotion': 'warning',
    'action': 'danger',
    'psychology': 'info'
  }
  return typeMap[type] || 'info'
}

const getCorpusTypeText = (type) => {
  const textMap = {
    'description': '场景描述',
    'dialogue': '对话模板',
    'emotion': '情感表达',
    'action': '动作描写',
    'psychology': '心理描写'
  }
  return textMap[type] || type
}

const addCorpus = () => {
  corpusForm.value = {
    id: null,
    title: '',
    type: 'description',
    content: '',
    tags: []
  }
  showCorpusDialog.value = true
}

const editCorpus = (corpus) => {
  corpusForm.value = { ...corpus }
  showCorpusDialog.value = true
}

const deleteCorpus = async (corpus) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除语料"${corpus.title}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (!props.novelId) {
      ElMessage.error('小说信息不存在')
      return
    }

    try {
      // 调用后端API删除语料
      await corpusApi.deleteCorpus(props.novelId, corpus.id)

      // 从本地语料列表中移除
      const index = corpusData.value.findIndex(item => item.id === corpus.id)
      if (index > -1) {
        corpusData.value.splice(index, 1)
        ElMessage.success('语料删除成功')
        emit('corpus-updated', corpusData.value)
      }
    } catch (error) {
      console.error('删除语料失败:', error)
      ElMessage.error('删除语料失败: ' + (error.message || '未知错误'))
    }
  } catch {
    // 用户取消删除
  }
}

const saveCorpus = async () => {
  if (!corpusForm.value.title.trim()) {
    ElMessage.warning('请输入语料标题')
    return
  }

  if (!props.novelId) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    if (corpusForm.value.id) {
      // 编辑现有语料
      const corpusDataToUpdate = {
        title: corpusForm.value.title,
        type: corpusForm.value.type,
        content: corpusForm.value.content,
        tags: corpusForm.value.tags || []
      }

      const updatedCorpus = await corpusApi.updateCorpus(
        props.novelId,
        corpusForm.value.id,
        corpusDataToUpdate
      )

      // 更新本地数据
      const index = corpusData.value.findIndex(c => c.id === corpusForm.value.id)
      if (index > -1) {
        corpusData.value[index] = {
          ...updatedCorpus,
          createdAt: new Date(updatedCorpus.createdAt),
          updatedAt: new Date(updatedCorpus.updatedAt)
        }
      }
      ElMessage.success('语料信息已更新')
    } else {
      // 新增语料
      const corpusDataToSave = {
        title: corpusForm.value.title,
        type: corpusForm.value.type,
        content: corpusForm.value.content,
        tags: corpusForm.value.tags || []
      }

      const newCorpus = await corpusApi.createCorpus(props.novelId, corpusDataToSave)

      // 添加到本地语料列表
      corpusData.value.push({
        ...newCorpus,
        createdAt: new Date(newCorpus.createdAt),
        updatedAt: new Date(newCorpus.updatedAt)
      })

      ElMessage.success('语料创建成功')
    }
  } catch (error) {
    console.error('保存语料失败:', error)
    ElMessage.error('保存语料失败: ' + (error.message || '未知错误'))
    return
  }

  showCorpusDialog.value = false
  emit('corpus-updated', corpusData.value)
}

// 加载语料库数据
const loadCorpusData = async () => {
  if (!props.novelId) return

  try {
    corpusLoading.value = true
    const response = await corpusApi.getCorpusList(props.novelId)
    console.log('语料库数据响应:', response)

    // 处理后端响应数据结构
    let corpusDataArray = []
    if (response && response.data) {
      if (Array.isArray(response.data)) {
        corpusDataArray = response.data
      } else {
        console.warn('response.data 不是数组格式:', response.data)
        corpusDataArray = []
      }
    } else if (response && Array.isArray(response)) {
      corpusDataArray = response
    } else {
      console.warn('未知的响应数据格式:', response)
      corpusDataArray = []
    }

    corpusData.value = corpusDataArray.map(corpus => ({
      ...corpus,
      createdAt: corpus.createdAt ? new Date(corpus.createdAt) : new Date(),
      updatedAt: corpus.updatedAt ? new Date(corpus.updatedAt) : new Date()
    }))

    console.log('从后端加载语料库数据:', corpusData.value.length, '个语料')
    emit('corpus-updated', corpusData.value)
  } catch (error) {
    console.error('加载语料库数据失败:', error)
    ElMessage.warning('语料库数据加载失败')
  } finally {
    corpusLoading.value = false
  }
}

// 暴露给父组件的方法和数据
defineExpose({
  corpusData,
  corpusCount,
  loadCorpusData,
  addCorpus
})

// 生命周期
onMounted(() => {
  loadCorpusData()
})
</script>

<style scoped>
/* 通用列表样式 */
.corpus-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  max-height: calc(100vh - 190px);
}

.corpus-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: column;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.corpus-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.corpus-content {
  flex: 1;
  text-align: left;
  width: 100%;
}

.corpus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.corpus-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.corpus-preview {
  margin: 8px 0;
  font-size: 13px;
  color: #606266;
}

.corpus-preview-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.corpus-preview-truncated:hover {
  color: #303133;
}

.corpus-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  width: 100%;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.panel-content {
  height: calc(100vh - 150px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}
</style>
