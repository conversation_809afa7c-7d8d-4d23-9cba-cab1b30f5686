<template>
  <el-dialog v-model="dialogVisible" title="编辑事件" width="600px" @close="handleClose">
    <el-form :model="localEventForm" label-width="80px">
      <el-form-item label="事件标题">
        <el-input v-model="localEventForm.title" placeholder="请输入事件标题" />
      </el-form-item>
      <el-form-item label="相关章节">
        <el-select v-model="localEventForm.chapter" placeholder="选择章节">
          <el-option
            v-for="chapter in chapters"
            :key="chapter.id"
            :label="chapter.title"
            :value="chapter.title"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间线">
        <el-input v-model="localEventForm.time" placeholder="如：第三天傍晚" />
      </el-form-item>
      <el-form-item label="重要程度">
        <el-radio-group v-model="localEventForm.importance">
          <el-radio label="low">次要</el-radio>
          <el-radio label="normal">一般</el-radio>
          <el-radio label="high">重要</el-radio>
          <el-radio label="critical">关键</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="事件描述">
        <el-input 
          v-model="localEventForm.description" 
          type="textarea" 
          :rows="4" 
          placeholder="请描述事件的详细内容..."
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  eventForm: {
    type: Object,
    required: true
  },
  chapters: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'update:modelValue',
  'save',
  'cancel'
])

// 响应式数据
const localEventForm = ref({
  id: null,
  title: '',
  description: '',
  chapter: '',
  time: '',
  importance: 'normal'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听 eventForm 变化，同步到本地表单
watch(() => props.eventForm, (newForm) => {
  if (newForm) {
    localEventForm.value = {
      id: newForm.id || null,
      title: newForm.title || '',
      description: newForm.description || '',
      chapter: newForm.chapter || '',
      time: newForm.time || '',
      importance: newForm.importance || 'normal'
    }
  }
}, { immediate: true, deep: true })

// 方法
const handleSave = () => {
  // 验证表单数据
  if (!localEventForm.value.title.trim()) {
    ElMessage.warning('请输入事件标题')
    return
  }

  // 将本地表单数据传递给父组件
  emit('save', localEventForm.value)
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

const handleClose = () => {
  emit('cancel')
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
}

.el-select {
  width: 100%;
}
</style>
