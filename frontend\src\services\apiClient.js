import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const apiClient = axios.create({
  baseURL: 'http://175.178.214.226:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    const { data } = response

    // 如果后端返回的是标准格式 {code, message, data}
    if (data.code !== undefined) {
      if (data.code === 200) {
        return data.data
      } else {
        // 特殊处理认证相关错误
        if (data.code === 401) {
          handleAuthError(data.message || 'token已过期')
          return Promise.reject(new Error(data.message || 'token已过期'))
        }

        ElMessage.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }

    return data
  },
  error => {
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 统一处理认证失败
          const message = data?.message || '认证失败，请重新登录'
          handleAuthError(message)
          break
        case 403:
          ElMessage.error('禁止访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

/**
 * 统一处理认证错误
 */
function handleAuthError(message) {
  // 动态导入authUtils避免循环依赖
  import('./authApi').then(({ authUtils }) => {
    ElMessage.error(message)
    authUtils.redirectToLogin(message)
  }).catch(() => {
    // 降级处理
    ElMessage.error(message)
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('loginTime')
    window.location.href = '/login'
  })
}

export default apiClient
